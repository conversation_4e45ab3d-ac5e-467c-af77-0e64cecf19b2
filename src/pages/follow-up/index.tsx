import {
  ChannelBadge,
  type ChanneType,
  FollowUpBadge,
  type FollowUpStatusType,
  OpportunityBadge,
  type OpportunityType,
  ServiceBadge,
  type ServiceType,
  TaskStatusBadge,
} from "@components/badge";
import { Button, Container, Select } from "@components/common";
import { RouthPath } from "@enums/route-path";
import { CaretRightIcon, CheckCircleIcon, ConfettiIcon } from "@phosphor-icons/react";
import { useNavigate } from "@tanstack/react-router";
import { cn } from "@utils/cn";
import { useTranslation } from "react-i18next";
import type { LeadProps } from "./interface";
import { leads } from "./mock";

interface PageTitleBarProps {
  leadCount: number;
}

const PageTitleBar = ({ leadCount }: PageTitleBarProps) => {
  const { t } = useTranslation();
  return (
    <div className="my-4 flex w-full justify-between">
      <div className="flex h-fit items-center gap-2">
        <h3>{t("followUp.followUp")}</h3>
        <div className="size-6 place-content-center rounded-full bg-secondary">
          <h6 className="place-self-center text-white">{leadCount}</h6>
        </div>
      </div>
      <Select className="w-full" size="sm" />
    </div>
  );
};

const TabalTitleBar = () => {
  const { t } = useTranslation();
  return (
    <div
      className={cn(
        "grid grid-cols-[2%_10%_10%_17%_1fr_10%_10%_10%_1%]",
        "place-items-center rounded-lg bg-primary p-4 text-white",
      )}
    >
      <div />
      <h6>{t("followUp.opportunity")}</h6>
      <h6>{t("followUp.contactChannel")}</h6>
      <h6>{t("followUp.name")}</h6>
      <h6>{t("followUp.servicesOfInterest")}</h6>
      <h6>{t("followUp.followUpStatus")}</h6>
      <h6>{t("followUp.assignee")}</h6>
      <h6>{t("followUp.tasksStatus")}</h6>
      <div />
    </div>
  );
};

const TaskDetail = ({ lead }: { lead: LeadProps }) => {
  const navigate = useNavigate();
  return (
    <div className="collapse-content peer-checked:bg-base-200">
      <div className="!rounded-2xl gap-2 border-container border-info p-4">
        <div className="flex w-full justify-between">
          <TaskStatusBadge
            type={lead.isCompleted ? "completed" : "onboarding"}
            label={lead.taskTitle}
          />
          {lead.isCompleted ? (
            <div className="flex items-center gap-2 rounded-xl bg-success-content p-2 text-primary">
              <ConfettiIcon size={24} />
              <h6>Complete</h6>
            </div>
          ) : (
            <Button variant="secondary">Done</Button>
          )}
        </div>
        <div className="flex gap-4">
          <p
            className={cn("!rounded-2xl !bg-base-200 flex-1 whitespace-pre-wrap border-container", {
              "text-info": lead.isCompleted,
            })}
          >
            {lead.taskDescription}
          </p>
          <div className="!rounded-2xl !w-1/3 gap-2 border-container">
            <p className="text-label-xs">Source:</p>
            <a href={lead.taskSource} className="text-blue-400 text-body-sm underline">
              {lead.taskSource}
            </a>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex flex-col gap-2">
            <h6>
              ติดต่อ <span className="text-body-sm">{lead.contactInfo}</span>
            </h6>
            <h6>บันทึก</h6>
            <p className="text-body-sm">{lead.note}</p>
          </div>
          <Button
            variant="outline"
            className="h-fit border-primary px-2 py-1 text-primary"
            onClick={() => navigate({ to: `${RouthPath.PROFILE}/${lead.name}` })}
          >
            <p className="text-label-xs">Lead Profile</p>
            <CaretRightIcon size={14} weight="bold" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const LeadRow = ({ lead, index }: { lead: LeadProps; index: number }) => {
  return (
    <div className="collapse-arrow collapse rounded-none border-base-300 border-b peer-checked:bg-base-200">
      <input type="checkbox" className="peer" />
      <div
        className={cn(
          "grid h-12 cursor-pointer grid-cols-[3%_10%_10%_16%_1fr_10%_10%_10%_2%]",
          "place-items-center p-0",
          "collapse-title hover:bg-success-content/50",
        )}
      >
        <h6>{index + 1}.</h6>
        <OpportunityBadge type={lead.opportunity as OpportunityType} />
        <ChannelBadge type={lead.contactChannel as ChanneType} />
        <p className="text-body-sm">{lead.name}</p>
        <ServiceBadge type={lead.servicesOfInterest as ServiceType} />
        <FollowUpBadge type={lead.followUpStatus as FollowUpStatusType} />
        <div className="avatar">
          <div className="w-8 rounded-full">
            <img
              alt="User Avatar"
              src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
            />
          </div>
        </div>
        <CheckCircleIcon
          size={24}
          weight="fill"
          className={cn(
            { "text-success": lead.isCompleted },
            { "text-base-300": !lead.isCompleted },
          )}
        />
      </div>
      <TaskDetail lead={lead} />
    </div>
  );
};

export function FollowUp() {
  const data = [...leads, ...leads, ...leads];
  return (
    <Container>
      <PageTitleBar leadCount={data.length} />
      <div className="flex-1 overflow-auto border-container">
        <TabalTitleBar />
        <div className="overflow-auto">
          {data.map((lead, i) => (
            <LeadRow key={lead.name} lead={lead} index={i} />
          ))}
        </div>
      </div>
    </Container>
  );
}
